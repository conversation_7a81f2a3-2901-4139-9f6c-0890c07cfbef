<template>
  <div style="padding: 20px;">
    <h2>图标选择器测试</h2>
    
    <div style="margin: 20px 0;">
      <label>选择图标：</label>
      <IconSelector v-model="selectedIcon" @change="onIconChange" />
    </div>
    
    <div style="margin: 20px 0;">
      <p>当前选中的图标：{{ selectedIcon || '无' }}</p>
      <div v-if="selectedIcon" style="margin-top: 10px;">
        <svg-icon :icon-class="selectedIcon" style="font-size: 24px;" />
        <span style="margin-left: 10px;">{{ selectedIcon }}</span>
      </div>
    </div>
    
    <div style="margin: 20px 0;">
      <h3>可用图标列表：</h3>
      <div style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 10px; margin-top: 10px;">
        <div 
          v-for="icon in availableIcons" 
          :key="icon"
          style="display: flex; flex-direction: column; align-items: center; padding: 10px; border: 1px solid #eee; border-radius: 4px;"
        >
          <svg-icon :icon-class="icon" style="font-size: 20px; margin-bottom: 5px;" />
          <span style="font-size: 12px; text-align: center;">{{ icon }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import IconSelector from '@/components/IconSelector'
import iconsIndex from '@/icons/index'

export default {
  name: 'TestIconSelector',
  components: {
    IconSelector
  },
  data() {
    return {
      selectedIcon: '',
      availableIcons: []
    }
  },
  created() {
    this.loadIcons()
  },
  methods: {
    loadIcons() {
      try {
        this.availableIcons = iconsIndex.getNameList()
        console.log('可用图标:', this.availableIcons)
      } catch (error) {
        console.error('加载图标失败:', error)
        this.availableIcons = []
      }
    },
    
    onIconChange(icon) {
      console.log('图标选择改变:', icon)
    }
  }
}
</script>
