<template>
  <div class="registration-form">
    <h4>报名填写项</h4>
    
    <!-- 系统预设项目 -->
    <div class="form-section">
      <h5>系统预设项目</h5>
      <div class="preset-items">
        <!-- 姓名 - 默认开启且必填，不可修改 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.name.enabled" disabled>姓名</el-checkbox>
          <el-checkbox v-model="presetItems.name.required" disabled>必填</el-checkbox>
        </div>
        
        <!-- 手机号 - 默认开启且必填，不可修改 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.mobile.enabled" disabled>手机号</el-checkbox>
          <el-checkbox v-model="presetItems.mobile.required" disabled>必填</el-checkbox>
        </div>
        
        <!-- 身份证 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.idNo.enabled">身份证</el-checkbox>
          <el-checkbox v-model="presetItems.idNo.required" :disabled="!presetItems.idNo.enabled">必填</el-checkbox>
        </div>
        
        <!-- 年龄 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.age.enabled">年龄</el-checkbox>
          <el-checkbox v-model="presetItems.age.required" :disabled="!presetItems.age.enabled">必填</el-checkbox>
        </div>
        
        <!-- 性别 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.gender.enabled">性别</el-checkbox>
          <el-checkbox v-model="presetItems.gender.required" :disabled="!presetItems.gender.enabled">必填</el-checkbox>
          <el-button 
            v-if="presetItems.gender.enabled" 
            type="text" 
            @click="editGenderOptions"
          >修改选项</el-button>
        </div>
        
        <!-- 证件类型 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.cardType.enabled">证件类型</el-checkbox>
          <el-checkbox v-model="presetItems.cardType.required" :disabled="!presetItems.cardType.enabled">必填</el-checkbox>
          <el-button 
            v-if="presetItems.cardType.enabled" 
            type="text" 
            @click="editCardTypeOptions"
          >修改选项</el-button>
        </div>
        
        <!-- 携带亲属 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.relative.enabled">携带亲属</el-checkbox>
          <el-checkbox v-model="presetItems.relative.required" :disabled="!presetItems.relative.enabled">必填</el-checkbox>
          <el-button 
            v-if="presetItems.relative.enabled" 
            type="text" 
            @click="editRelativeLimit"
          >设置上限</el-button>
        </div>
        
        <!-- 身高 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.height.enabled">身高</el-checkbox>
          <el-checkbox v-model="presetItems.height.required" :disabled="!presetItems.height.enabled">必填</el-checkbox>
        </div>
        
        <!-- 体重 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.weight.enabled">体重</el-checkbox>
          <el-checkbox v-model="presetItems.weight.required" :disabled="!presetItems.weight.enabled">必填</el-checkbox>
        </div>
        
        <!-- 学历 -->
        <div class="form-item">
          <el-checkbox v-model="presetItems.education.enabled">学历</el-checkbox>
          <el-checkbox v-model="presetItems.education.required" :disabled="!presetItems.education.enabled">必填</el-checkbox>
          <el-button 
            v-if="presetItems.education.enabled" 
            type="text" 
            @click="editEducationOptions"
          >修改选项</el-button>
        </div>
      </div>
    </div>
    
    <!-- 自增项目 -->
    <div class="form-section">
      <h5>自增项目</h5>
      <el-button type="primary" size="small" @click="addCustomItem">新增自增项目</el-button>
      
      <div v-if="customItems.length > 0" class="custom-items">
        <div v-for="(item, index) in customItems" :key="index" class="custom-item">
          <span>{{ item.name }}</span>
          <span class="item-type">({{ getComponentTypeName(item.componentType) }})</span>
          <el-switch v-model="item.enabled" />
          <el-checkbox v-model="item.required" :disabled="!item.enabled">必填</el-checkbox>
          <el-button type="text" @click="editCustomItem(index)">修改</el-button>
          <el-button type="text" style="color: #f56c6c" @click="deleteCustomItem(index)">删除</el-button>
        </div>
      </div>
    </div>
    
    <!-- 选项编辑弹窗 -->
    <el-dialog
      :title="optionDialog.title"
      :visible.sync="optionDialog.visible"
      width="400px"
    >
      <el-form :model="optionDialog.form" label-width="80px">
        <el-form-item label="选项:">
          <el-input
            v-model="optionDialog.form.options"
            type="textarea"
            :rows="4"
            placeholder="多个选项用英文逗号分隔"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="optionDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveOptions">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- 携带亲属上限设置弹窗 -->
    <el-dialog
      title="设置上限"
      :visible.sync="relativeDialog.visible"
      width="400px"
    >
      <el-form :model="relativeDialog.form" label-width="100px">
        <el-form-item label="携带大人:">
          <el-input-number 
            v-model="relativeDialog.form.adultLimit" 
            :min="0" 
            placeholder="为空表示不设限"
          />
        </el-form-item>
        <el-form-item label="携带小孩:">
          <el-input-number 
            v-model="relativeDialog.form.childLimit" 
            :min="0" 
            placeholder="为空表示不设限"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="relativeDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveRelativeLimit">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- 自增项目编辑弹窗 -->
    <el-dialog
      :title="customDialog.title"
      :visible.sync="customDialog.visible"
      width="500px"
    >
      <el-form :model="customDialog.form" :rules="customRules" ref="customForm" label-width="100px">
        <el-form-item label="内容名称:" prop="name">
          <el-input 
            v-model="customDialog.form.name" 
            placeholder="请输入内容名称"
            maxlength="10"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="组件:" prop="componentType">
          <el-radio-group v-model="customDialog.form.componentType">
            <el-radio label="text">单行文本框</el-radio>
            <el-radio label="textarea">多行文本框</el-radio>
            <el-radio label="select">单选下拉框</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item 
          v-if="customDialog.form.componentType === 'select'" 
          label="选项:" 
          prop="options"
        >
          <el-input
            v-model="customDialog.form.options"
            placeholder="多个选项用英文逗号分隔"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="customDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveCustomItem">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RegistrationForm',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      presetItems: {
        name: { enabled: true, required: true },
        mobile: { enabled: true, required: true },
        idNo: { enabled: false, required: false },
        age: { enabled: false, required: false },
        gender: { enabled: false, required: false, options: ['男', '女', '其他'] },
        cardType: { enabled: false, required: false, options: ['身份证', '护照', '其他'] },
        relative: { enabled: false, required: false, adultLimit: null, childLimit: null },
        height: { enabled: false, required: false },
        weight: { enabled: false, required: false },
        education: { enabled: false, required: false, options: ['初中', '高中', '大专', '本科', '硕士', '博士', '其他'] }
      },
      customItems: [],
      optionDialog: {
        visible: false,
        title: '',
        type: '',
        form: {
          options: ''
        }
      },
      relativeDialog: {
        visible: false,
        form: {
          adultLimit: null,
          childLimit: null
        }
      },
      customDialog: {
        visible: false,
        title: '',
        editIndex: -1,
        form: {
          name: '',
          componentType: 'text',
          options: ''
        }
      },
      customRules: {
        name: [
          { required: true, message: '请输入内容名称', trigger: 'blur' }
        ],
        componentType: [
          { required: true, message: '请选择组件', trigger: 'change' }
        ],
        options: [
          { required: true, message: '请输入选项', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    presetItems: {
      handler() {
        this.emitChange()
      },
      deep: true
    },
    customItems: {
      handler() {
        this.emitChange()
      },
      deep: true
    }
  },
  methods: {
    // 初始化数据
    initData(data) {
      if (data.presetItems) {
        this.presetItems = { ...this.presetItems, ...data.presetItems }
      }
      if (data.customItems) {
        this.customItems = [...data.customItems]
      }
    },
    
    // 发送变更事件
    emitChange() {
      this.$emit('input', {
        presetItems: this.presetItems,
        customItems: this.customItems
      })
    },
    
    // 获取组件类型名称
    getComponentTypeName(type) {
      const typeMap = {
        text: '单行文本框',
        textarea: '多行文本框',
        select: '单选下拉框'
      }
      return typeMap[type] || type
    },

    // 编辑性别选项
    editGenderOptions() {
      this.optionDialog = {
        visible: true,
        title: '修改性别选项',
        type: 'gender',
        form: {
          options: this.presetItems.gender.options.join(',')
        }
      }
    },

    // 编辑证件类型选项
    editCardTypeOptions() {
      this.optionDialog = {
        visible: true,
        title: '修改证件类型选项',
        type: 'cardType',
        form: {
          options: this.presetItems.cardType.options.join(',')
        }
      }
    },

    // 编辑学历选项
    editEducationOptions() {
      this.optionDialog = {
        visible: true,
        title: '修改学历选项',
        type: 'education',
        form: {
          options: this.presetItems.education.options.join(',')
        }
      }
    },

    // 保存选项
    saveOptions() {
      const options = this.optionDialog.form.options.split(',').map(item => item.trim()).filter(item => item)
      if (options.length === 0) {
        this.$message.error('请输入至少一个选项')
        return
      }

      this.presetItems[this.optionDialog.type].options = options
      this.optionDialog.visible = false
      this.$message.success('保存成功')
    },

    // 编辑携带亲属上限
    editRelativeLimit() {
      this.relativeDialog = {
        visible: true,
        form: {
          adultLimit: this.presetItems.relative.adultLimit,
          childLimit: this.presetItems.relative.childLimit
        }
      }
    },

    // 保存携带亲属上限
    saveRelativeLimit() {
      this.presetItems.relative.adultLimit = this.relativeDialog.form.adultLimit
      this.presetItems.relative.childLimit = this.relativeDialog.form.childLimit
      this.relativeDialog.visible = false
      this.$message.success('保存成功')
    },

    // 新增自增项目
    addCustomItem() {
      this.customDialog = {
        visible: true,
        title: '新增自增项目',
        editIndex: -1,
        form: {
          name: '',
          componentType: 'text',
          options: ''
        }
      }
    },

    // 编辑自增项目
    editCustomItem(index) {
      const item = this.customItems[index]
      this.customDialog = {
        visible: true,
        title: '修改自增项目',
        editIndex: index,
        form: {
          name: item.name,
          componentType: item.componentType,
          options: item.options ? item.options.join(',') : ''
        }
      }
    },

    // 保存自增项目
    saveCustomItem() {
      this.$refs.customForm.validate((valid) => {
        if (!valid) return

        const formData = { ...this.customDialog.form }

        // 处理选项
        if (formData.componentType === 'select') {
          if (!formData.options.trim()) {
            this.$message.error('请输入选项')
            return
          }
          formData.options = formData.options.split(',').map(item => item.trim()).filter(item => item)
        } else {
          delete formData.options
        }

        const item = {
          ...formData,
          enabled: true,
          required: true
        }

        if (this.customDialog.editIndex >= 0) {
          // 编辑
          this.$set(this.customItems, this.customDialog.editIndex, item)
        } else {
          // 新增
          this.customItems.push(item)
        }

        this.customDialog.visible = false
        this.$message.success('保存成功')
      })
    },

    // 删除自增项目
    deleteCustomItem(index) {
      this.$confirm('确定删除该自增项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.customItems.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.registration-form {
  .form-section {
    margin-bottom: 20px;

    h5 {
      margin-bottom: 10px;
      color: #303133;
      font-weight: 600;
    }
  }

  .preset-items {
    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .el-checkbox {
        margin-right: 15px;
      }

      .el-button--text {
        margin-left: 10px;
      }
    }
  }

  .custom-items {
    margin-top: 15px;

    .custom-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-bottom: 10px;

      span {
        margin-right: 10px;

        &.item-type {
          color: #909399;
          font-size: 12px;
        }
      }

      .el-switch {
        margin-right: 10px;
      }

      .el-checkbox {
        margin-right: 10px;
      }

      .el-button--text {
        margin-left: 5px;
      }
    }
  }
}
</style>
</script>
