# 活动管理系统

## 概述
本系统实现了完整的活动管理功能，包括活动的创建、管理、发布等功能，满足银行活动管理的各种需求。

## 功能特性

### 1. 活动列表管理
- **查询功能**：
  - 活动编号（精确搜索）
  - 活动名称（模糊搜索）
  - 活动类型下拉选择
  - 活动门槛多选（所有用户、普通客户、潜力客户、金卡客户、白金卡客户、钻石客户、私人银行客户）
  - 上架状态（草稿、已上架、已下架）
  - 活动状态（活动未开始、活动进行中、活动已结束）
  - 报名状态（报名未开始、报名进行中、报名已结束）
  - 创建时间范围查询

- **列表展示**：
  - 活动编号、活动名称、活动标题
  - 排序、活动类型、活动门槛
  - 活动名额、上架状态、活动状态、报名状态
  - 创建时间、更新时间、更新者
  - 操作按钮（根据状态动态显示）

### 2. 活动操作功能
- **活动签到码**：生成二维码，支持下载为图片
- **详情查看**：查看活动完整信息
- **修改编辑**：编辑活动信息（草稿/已下架状态）
- **上架/下架**：控制活动发布状态
- **复制活动**：快速创建相似活动
- **删除活动**：逻辑删除（草稿/已下架状态）
- **导出功能**：导出活动列表为Excel

### 3. 活动创建/编辑
- **基本信息**：
  - 活动名称（必填，50字限制）
  - 活动标题（必填，50字限制）
  - 活动类型（必填，下拉选择）
  - 头图上传（必填，支持多张，单张500k限制）
  - 排序（非必填，默认0）

- **活动设置**：
  - 活动门槛（多选）
  - 报名时间（必填，精确到秒）
  - 活动时间（必填，精确到秒，需验证时间逻辑）
  - 活动地点（必填，50字限制）
  - 详细地址（非必填，50字限制）
  - 活动名额（必填，0表示不限制）

- **内容编辑**：
  - 活动须知（必填，200字限制）
  - 活动介绍（必填，支持富文本）

- **报名填写项配置**：
  - 系统预设项目（姓名、手机号、身份证等）
  - 自增项目（支持单行文本、多行文本、单选下拉框）
  - 灵活的开关和必填设置

## 技术实现

### 文件结构
```
src/views/act-manage/
├── actManage.vue                    # 活动管理主页面
├── actTypeList.vue                  # 活动类型管理页面
└── components/
    ├── activityDialog.vue           # 活动编辑对话框
    └── registrationForm.vue         # 报名填写项配置组件
```

### API接口
```javascript
// 活动管理相关接口
- getActivityList()      # 获取活动列表
- getActivityDetail()    # 获取活动详情
- addActivity()          # 新增活动
- modifyActivity()       # 修改活动
- onSaleActivity()       # 上架活动
- downSaleActivity()     # 下架活动
- deleteActivity()       # 删除活动
- exportActivityExcel()  # 导出活动表格

// 活动类型管理相关接口
- getActivityType()      # 获取活动类型列表
- addActivityType()      # 新增活动类型
- modifyActivityType()   # 修改活动类型
- deleteActivityType()   # 删除活动类型
```

### 依赖包
- `qrcode`: 用于生成二维码
- `html2canvas`: 用于将DOM元素转换为图片

## 状态管理

### 上架状态
- 0: 已下架
- 1: 已上架
- 2: 草稿

### 活动状态（自动计算）
- 1: 活动未开始
- 2: 活动进行中
- 3: 活动已结束

### 报名状态（自动计算）
- 1: 报名未开始
- 2: 报名进行中
- 3: 报名已结束

## 业务规则

1. **时间验证**：报名结束时间必须 ≤ 活动开始时间
2. **操作权限**：
   - 修改/上架/删除：仅限草稿和已下架状态
   - 下架：仅限已上架状态
   - 签到码：仅限非草稿状态
3. **数据保留**：删除活动时，历史订单和统计数据保留
4. **图片要求**：支持jpg、png、JPEG、gif、svg格式，单张不超过500k

## 使用说明

1. **新增活动**：点击"新增"按钮，填写活动信息，保存后状态为草稿
2. **编辑活动**：选择草稿或已下架的活动，点击"修改"进行编辑
3. **发布活动**：草稿状态的活动点击"上架"后用户端可见
4. **管理活动**：通过上架/下架控制活动的可见性
5. **签到管理**：已上架活动可生成签到码供现场使用
6. **数据导出**：支持按查询条件导出活动数据

## 注意事项

1. 活动类型需要在"活动类型管理"中预先配置
2. 上传的图片会自动压缩和处理
3. 报名填写项的配置会影响用户端的报名表单
4. 删除操作为逻辑删除，数据仍保留在数据库中
5. 时间设置需要考虑用户的报名和参与时间安排

## 后续扩展

1. 可以添加活动审核流程
2. 支持活动模板功能
3. 增加活动数据统计和分析
4. 支持批量操作功能
5. 添加活动推送和通知功能
