import { asyncRoutes, constantRoutes } from '@/router'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route && route.path) {
    return roles.indexOf(route.path) > -1
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * Generate routes from backend menu data
 * @param menuList backend menu data
 * @param roles user roles
 */
function generateRoutesFromMenu(menuList, roles) {
  if (!menuList || !Array.isArray(menuList)) {
    return []
  }

  // Create a map of frontend routes for quick lookup
  const routeMap = {}
  function buildRouteMap(routes) {
    routes.forEach(route => {
      if (route.path) {
        routeMap[route.path] = route
      }
      if (route.children) {
        buildRouteMap(route.children)
      }
    })
  }
  buildRouteMap(asyncRoutes)

  // Generate routes from menu data
  function generateRoutes(menus) {
    return menus
      .filter(menu => {
        // Check if user has permission for this menu
        return roles.includes(menu.serverPermPath) || roles.includes(menu.menuId)
      })
      .sort((a, b) => {
        // Sort by backend sort order
        return parseInt(a.sort || 0) - parseInt(b.sort || 0)
      })
      .map(menu => {
        // Find corresponding frontend route
        const frontendRoute = routeMap[menu.serverPermPath] || routeMap[menu.menuId]

        if (frontendRoute) {
          // Use frontend route as base and override with backend data
          const route = {
            ...frontendRoute,
            meta: {
              ...frontendRoute.meta,
              title: menu.menuName,
              // Use backend icon if available, otherwise keep frontend icon
              icon: menu.permIcon || (frontendRoute.meta && frontendRoute.meta.icon),
              // Keep frontend icon assets for compatibility
              icon_o: frontendRoute.meta && frontendRoute.meta.icon_o,
              icon_c: frontendRoute.meta && frontendRoute.meta.icon_c
            }
          }

          // Process children recursively
          if (menu.subMenu && menu.subMenu.length > 0) {
            const childRoutes = generateRoutes(menu.subMenu)
            if (childRoutes.length > 0) {
              route.children = childRoutes
            }
          }

          return route
        } else {
          // Create a basic route structure for menus without frontend route
          return {
            path: menu.serverPermPath || menu.menuId,
            name: menu.menuName,
            meta: {
              title: menu.menuName,
              icon: menu.permIcon
            },
            children: menu.subMenu && menu.subMenu.length > 0 ? generateRoutes(menu.subMenu) : undefined
          }
        }
      })
      .filter(route => route !== null)
  }

  return generateRoutes(menuList)
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, { roles, menuList }) {
    return new Promise(resolve => {
      // Generate routes directly from backend menu data
      const menuRoutes = generateRoutesFromMenu(menuList || [], roles)

      // Also include any frontend-only routes that have permissions
      const frontendRoutes = filterAsyncRoutes(asyncRoutes, roles)

      // Combine menu routes with frontend routes, prioritizing menu routes
      const menuPaths = new Set()
      function collectPaths(routes) {
        routes.forEach(route => {
          menuPaths.add(route.path)
          if (route.children) {
            collectPaths(route.children)
          }
        })
      }
      collectPaths(menuRoutes)

      // Add frontend routes that are not covered by menu routes
      const additionalRoutes = frontendRoutes.filter(route => !menuPaths.has(route.path))

      const accessedRoutes = [...menuRoutes, ...additionalRoutes]
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
