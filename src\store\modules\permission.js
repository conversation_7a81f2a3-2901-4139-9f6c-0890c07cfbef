import { asyncRoutes, constantRoutes } from '@/router'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route && route.path) {
    return roles.indexOf(route.path) > -1
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * Sort routes based on backend menu order
 * @param routes asyncRoutes
 * @param menuList backend menu data
 */
function sortRoutesByBackendOrder(routes, menuList) {
  // Create a map of menu paths to their sort values
  const menuSortMap = {}
  function buildMenuSortMap(menus) {
    menus.forEach(menu => {
      if (menu.serverPermPath) {
        menuSortMap[menu.serverPermPath] = menu.sort
      }
      if (menu.subMenu && menu.subMenu.length > 0) {
        buildMenuSortMap(menu.subMenu)
      }
    })
  }
  buildMenuSortMap(menuList)

  // Sort routes recursively
  function sortRoutes(routesToSort) {
    return routesToSort
      .sort((a, b) => {
        // Use backend sort order if available, otherwise keep original order
        const aSort = menuSortMap[a.path] || 0
        const bSort = menuSortMap[b.path] || 0
        return aSort - bSort
      })
      .map(route => {
        if (route.children) {
          return { ...route, children: sortRoutes(route.children) }
        }
        return route
      })
  }

  return sortRoutes(routes)
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, { roles, menuList }) {
    return new Promise(resolve => {
      // Sort routes by backend menu order first
      const sortedRoutes = sortRoutesByBackendOrder(asyncRoutes, menuList || [])
      // Then filter based on roles
      const accessedRoutes = filterAsyncRoutes(sortedRoutes, roles)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
