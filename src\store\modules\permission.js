import { asyncRoutes, constantRoutes } from '@/router'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route && route.path) {
    return roles.indexOf(route.path) > -1
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * Generate routes from backend menu data
 * @param menuList backend menu data
 * @param roles user roles
 */
function generateRoutesFromMenu(menuList, roles) {
  if (!menuList || !Array.isArray(menuList)) {
    return []
  }

  // Create a map of frontend routes for quick lookup
  const routeMap = {}
  function buildRouteMap(routes) {
    routes.forEach(route => {
      if (route.path) {
        routeMap[route.path] = route
      }
      if (route.children) {
        buildRouteMap(route.children)
      }
    })
  }
  // Build map from both constant and async routes
  buildRouteMap([...constantRoutes, ...asyncRoutes])

  // Generate routes from menu data
  function generateRoutes(menus) {
    return menus
      .filter(menu => {
        // Check if user has permission for this menu
        return roles.includes(menu.serverPermPath) || roles.includes(menu.menuId)
      })
      .sort((a, b) => {
        // Sort by backend sort order
        return parseInt(a.sort || 0) - parseInt(b.sort || 0)
      })
      .map(menu => {
        // Find corresponding frontend route by multiple possible paths
        let frontendRoute = null

        // Try to find route by serverPermPath, menuId, or partial match
        if (menu.serverPermPath) {
          frontendRoute = routeMap[menu.serverPermPath]
        }
        if (!frontendRoute && menu.menuId) {
          frontendRoute = routeMap[menu.menuId]
        }

        // If still not found, try to find by path matching
        if (!frontendRoute) {
          const possiblePaths = [menu.serverPermPath, menu.menuId].filter(Boolean)
          for (const path of possiblePaths) {
            const matchedRoute = Object.values(routeMap).find(route =>
              route.path === path ||
              route.path.includes(path) ||
              path.includes(route.path)
            )
            if (matchedRoute) {
              frontendRoute = matchedRoute
              break
            }
          }
        }

        console.log(`菜单 ${menu.menuName} (${menu.serverPermPath || menu.menuId}) 匹配到路由:`, frontendRoute?.path)

        if (frontendRoute) {
          // Use frontend route as base and override with backend data
          const route = {
            ...frontendRoute,
            meta: {
              ...frontendRoute.meta,
              title: menu.menuName,
              // Use backend icon if available, otherwise keep frontend icon
              icon: menu.permIcon || (frontendRoute.meta && frontendRoute.meta.icon),
              // Keep frontend icon assets for compatibility
              icon_o: frontendRoute.meta && frontendRoute.meta.icon_o,
              icon_c: frontendRoute.meta && frontendRoute.meta.icon_c
            }
          }

          // Process children recursively
          if (menu.subMenu && menu.subMenu.length > 0) {
            const childRoutes = generateRoutes(menu.subMenu)
            if (childRoutes.length > 0) {
              route.children = childRoutes
            }
          }

          return route
        } else {
          // Create a basic route structure for menus without frontend route
          console.warn(`No frontend route found for menu: ${menu.menuName} (${menu.serverPermPath || menu.menuId})`)
          return null
        }
      })
      .filter(route => route !== null)
  }

  return generateRoutes(menuList)
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    // Get hidden routes from constantRoutes (like login, 404, etc.)
    const hiddenRoutes = constantRoutes.filter(route => route.hidden)
    // Routes order is now completely controlled by backend menu data
    state.routes = [...hiddenRoutes, ...routes]
  }
}

const actions = {
  generateRoutes({ commit }, { roles, menuList }) {
    return new Promise(resolve => {
      console.log('开始生成路由，菜单数据：', menuList)
      console.log('用户权限：', roles)

      // Generate routes directly from backend menu data
      const menuRoutes = generateRoutesFromMenu(menuList || [], roles)
      console.log('生成的菜单路由：', menuRoutes)

      // Get hidden routes from constantRoutes (like login, 404, etc.)
      const hiddenRoutes = constantRoutes.filter(route => route.hidden)

      // Combine hidden routes with menu routes
      // Menu routes will determine the display order completely
      const accessedRoutes = [...hiddenRoutes, ...menuRoutes]

      console.log('最终路由：', accessedRoutes)
      commit('SET_ROUTES', menuRoutes) // Only pass menu routes to SET_ROUTES
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
