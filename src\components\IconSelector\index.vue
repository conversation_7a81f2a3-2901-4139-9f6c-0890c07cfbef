<template>
  <div class="icon-selector">
    <el-popover
      placement="bottom-start"
      width="400"
      trigger="click"
      v-model="visible"
    >
      <div class="icon-selector-content">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchText"
            placeholder="搜索图标"
            prefix-icon="el-icon-search"
            size="small"
            clearable
          />
        </div>
        
        <!-- 图标网格 -->
        <div class="icon-grid">
          <div
            v-for="icon in filteredIcons"
            :key="icon"
            class="icon-item"
            :class="{ active: selectedIcon === icon }"
            @click="selectIcon(icon)"
          >
            <svg-icon :icon-class="icon" class="icon" />
            <span class="icon-name">{{ icon }}</span>
          </div>
        </div>
        
        <!-- 清除选择 -->
        <div class="actions">
          <el-button size="small" @click="clearSelection">清除</el-button>
          <el-button type="primary" size="small" @click="confirmSelection">确定</el-button>
        </div>
      </div>
      
      <!-- 触发器 -->
      <div slot="reference" class="icon-selector-trigger">
        <div class="selected-icon" v-if="value">
          <svg-icon :icon-class="value" />
          <span>{{ value }}</span>
        </div>
        <div class="placeholder" v-else>
          <i class="el-icon-plus"></i>
          <span>选择图标</span>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import iconsIndex from '@/icons/index'

export default {
  name: 'IconSelector',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      searchText: '',
      selectedIcon: this.value,
      allIcons: []
    }
  },
  computed: {
    filteredIcons() {
      if (!this.searchText) {
        return this.allIcons
      }
      return this.allIcons.filter(icon => 
        icon.toLowerCase().includes(this.searchText.toLowerCase())
      )
    }
  },
  watch: {
    value(newVal) {
      this.selectedIcon = newVal
    }
  },
  created() {
    this.loadIcons()
  },
  methods: {
    // 加载所有图标
    loadIcons() {
      try {
        this.allIcons = iconsIndex.getNameList()
      } catch (error) {
        console.error('加载图标失败:', error)
        this.allIcons = []
      }
    },
    
    // 选择图标
    selectIcon(icon) {
      this.selectedIcon = icon
    },
    
    // 确认选择
    confirmSelection() {
      this.$emit('input', this.selectedIcon)
      this.$emit('change', this.selectedIcon)
      this.visible = false
    },
    
    // 清除选择
    clearSelection() {
      this.selectedIcon = ''
      this.$emit('input', '')
      this.$emit('change', '')
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-selector {
  .icon-selector-trigger {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    min-height: 32px;
    display: flex;
    align-items: center;
    
    &:hover {
      border-color: #c0c4cc;
    }
    
    .selected-icon {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .svg-icon {
        font-size: 16px;
      }
    }
    
    .placeholder {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #c0c4cc;
      
      i {
        font-size: 16px;
      }
    }
  }
}

.icon-selector-content {
  .search-box {
    margin-bottom: 12px;
  }
  
  .icon-grid {
    max-height: 300px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 12px;
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        border-color: #409eff;
        background-color: #f5f7fa;
      }
      
      &.active {
        border-color: #409eff;
        background-color: #ecf5ff;
      }
      
      .icon {
        font-size: 20px;
        margin-bottom: 4px;
      }
      
      .icon-name {
        font-size: 12px;
        color: #666;
        text-align: center;
        word-break: break-all;
      }
    }
  }
  
  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
