<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <!-- 优先使用后端图标，如果没有则使用前端图标 -->
          <svg-icon
            v-if="onlyOneChild.meta.icon && !onlyOneChild.meta.icon_o"
            :icon-class="onlyOneChild.meta.icon"
            class="menu-icon"
          />
          <img
            v-else-if="onlyOneChild.meta.icon_o && onlyOneChild.meta.icon_c"
            class="img"
            :src="(activeId.search(onlyOneChild.path) !=-1) ? onlyOneChild.meta.icon_o : onlyOneChild.meta.icon_c"
            alt=""
          >
          <span>{{ onlyOneChild.meta.title }}</span>
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <!-- 优先使用后端图标，如果没有则使用前端图标 -->
        <svg-icon
          v-if="item.meta.icon && !item.meta.icon_o"
          :icon-class="item.meta.icon"
          class="menu-icon"
        />
        <img
          v-else-if="item.meta.icon_o && item.meta.icon_c"
          class="img"
          :src="activeId.search(item.path) !=-1 ? item.meta.icon_o : item.meta.icon_c"
          alt=""
        >
        <span :style="activeId.search(item.path) !=-1? 'color: #409EFF;':'color: #000000;'">{{ item.meta.title }}</span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
// import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {
      activeId: ''
    }
  },
  watch: {
    $route(route) {
      this.activeId = this.$route.path
      // console.log("watch path",this.activeId);
    }
  },
  mounted() {
    // console.log("item",this.item)
  },
  created() {
    this.activeId = this.$route.path
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })
      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        if (parent.meta && !parent.meta.hasOwnProperty('iconShow') && !parent.meta.iconShow) {
          this.onlyOneChild.meta.icon_o = parent.meta.icon_o
          this.onlyOneChild.meta.icon_c = parent.meta.icon_c
        }
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, noShowingChildren: true }
        // console.log('onlyOneChild',this.onlyOneChild)
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>

<style lang="scss" scoped>
.img {
  width: 20px;
  height: 20px;
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  font-size: 18px;
}

.nest-menu{
  height:50px;
}
</style>
