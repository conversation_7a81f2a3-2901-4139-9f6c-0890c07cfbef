<template>
  <div style="padding: 20px;">
    <h2>菜单数据调试页面</h2>
    
    <el-button @click="refreshData" type="primary">刷新数据</el-button>
    <el-button @click="showRawData = !showRawData">{{ showRawData ? '隐藏' : '显示' }}原始数据</el-button>
    
    <div v-if="showRawData" style="margin: 20px 0;">
      <h3>后端菜单原始数据：</h3>
      <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">
        <pre>{{ JSON.stringify(rawMenuData, null, 2) }}</pre>
      </div>
    </div>
    
    <div style="margin: 20px 0;">
      <h3>处理后的菜单数据：</h3>
      <el-table :data="processedMenuData" border style="width: 100%">
        <el-table-column prop="menuName" label="菜单名称" width="200" />
        <el-table-column prop="serverPermPath" label="路径" width="200" />
        <el-table-column prop="sort" label="排序" width="100" />
        <el-table-column prop="permIcon" label="图标" width="150">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; gap: 10px;">
              <svg-icon v-if="scope.row.permIcon" :icon-class="scope.row.permIcon" />
              <span>{{ scope.row.permIcon || '无' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="subMenuCount" label="子菜单数量" width="120" />
      </el-table>
    </div>
    
    <div style="margin: 20px 0;">
      <h3>当前显示的路由：</h3>
      <el-table :data="currentRoutes" border style="width: 100%">
        <el-table-column prop="path" label="路径" width="200" />
        <el-table-column label="标题" width="200">
          <template slot-scope="scope">
            {{ scope.row.meta ? scope.row.meta.title : '' }}
          </template>
        </el-table-column>
        <el-table-column label="图标" width="150">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; gap: 10px;">
              <svg-icon v-if="scope.row.meta && scope.row.meta.icon" :icon-class="scope.row.meta.icon" />
              <img v-else-if="scope.row.meta && scope.row.meta.icon_o" :src="scope.row.meta.icon_o" style="width: 20px; height: 20px;" />
              <span>{{ (scope.row.meta && scope.row.meta.icon) || '无' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="hidden" label="隐藏" width="100">
          <template slot-scope="scope">
            {{ scope.row.hidden ? '是' : '否' }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div style="margin: 20px 0;">
      <h3>路由匹配测试：</h3>
      <div v-for="menu in flatMenuList" :key="menu.menuId" style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
        <div><strong>菜单：</strong>{{ menu.menuName }}</div>
        <div><strong>路径：</strong>{{ menu.serverPermPath }}</div>
        <div><strong>排序：</strong>{{ menu.sort }}</div>
        <div><strong>匹配状态：</strong>
          <span :style="{ color: getMatchStatus(menu) === '匹配成功' ? 'green' : 'red' }">
            {{ getMatchStatus(menu) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'DebugMenu',
  data() {
    return {
      showRawData: false,
      rawMenuData: [],
      processedMenuData: [],
      flatMenuList: []
    }
  },
  computed: {
    ...mapGetters([
      'permission_routes'
    ]),
    currentRoutes() {
      return this.permission_routes.filter(route => !route.hidden)
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      // 获取原始菜单数据
      this.rawMenuData = this.$store.state.user.menuList || []
      
      // 处理菜单数据
      this.processMenuData()
      
      // 扁平化菜单列表
      this.flattenMenuList()
    },
    
    processMenuData() {
      const processMenu = (menus) => {
        return menus.map(menu => ({
          menuName: menu.menuName,
          serverPermPath: menu.serverPermPath,
          sort: menu.sort,
          permIcon: menu.permIcon,
          subMenuCount: menu.subMenu ? menu.subMenu.length : 0
        }))
      }
      
      this.processedMenuData = processMenu(this.rawMenuData)
    },
    
    flattenMenuList() {
      const flatten = (menus, result = []) => {
        menus.forEach(menu => {
          result.push(menu)
          if (menu.subMenu && menu.subMenu.length > 0) {
            flatten(menu.subMenu, result)
          }
        })
        return result
      }
      
      this.flatMenuList = flatten(this.rawMenuData)
    },
    
    getMatchStatus(menu) {
      const matchedRoute = this.currentRoutes.find(route => 
        route.path === menu.serverPermPath || 
        route.path.includes(menu.serverPermPath) ||
        menu.serverPermPath.includes(route.path)
      )
      return matchedRoute ? '匹配成功' : '未匹配'
    },
    
    refreshData() {
      this.$store.dispatch('user/getInfo').then(() => {
        this.loadData()
        this.$message.success('数据刷新成功')
      }).catch(error => {
        this.$message.error('数据刷新失败: ' + error)
      })
    }
  }
}
</script>
