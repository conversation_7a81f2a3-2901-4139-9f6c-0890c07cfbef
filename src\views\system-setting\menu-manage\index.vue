<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <!-- 查询表单 -->
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="searchForm"
            label-width="80px"
            label-position="left"
            class="left"
            inline
          >
            <el-form-item label="菜单名">
              <el-input
                v-model.trim="searchForm.menuName"
                placeholder="请输入菜单名"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item label="菜单ID">
              <el-input
                v-model.trim="searchForm.menuId"
                placeholder="请输入菜单ID"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <el-button type="primary" @click="handleAdd">新增</el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="table">
          <el-table
            ref="tableRef"
            :data="tableData"
            row-key="menuId"
            default-expand-all
            :tree-props="{children: 'subMenu', }"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              prop="menuName"
              label="名称"
              min-width="200"
            />
            <el-table-column
              prop="pMenuName"
              label="上级菜单"
              width="150"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.pMenuName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="permIcon"
              label="图标"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <i v-if="scope.row.permIcon" :class="`el-icon-${scope.row.permIcon}`" />
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="permType"
              label="类型"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="getTypeTagType(scope.row.permType)"
                  size="small"
                >
                  {{ getTypeText(scope.row.permType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="sort"
              label="排序"
              width="80"
              align="center"
            />
            <el-table-column
              prop="serverPermPath"
              label="前端地址"
              width="150"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row.serverPermPath || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="后台权限地址"
              width="150"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>-</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="permDesc"
              label="描述"
              width="150"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row.permDesc || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission"
                  type="text"
                  @click="handleEdit(scope.row)"
                >修改</el-button>
                <el-button
                  v-if="deletePermission"
                  type="text"
                  @click="handleDelete(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              :current-page="pagination.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 菜单新增/编辑弹窗 -->
    <el-dialog
      :title="dialogObj.title"
      :visible.sync="dialogObj.visible"
      destroy-on-close
      width="800px"
      @close="dialogObj.visible = false"
    >
      <el-form
        ref="menuFormRef"
        :model="dialogObj.form"
        :rules="dialogObj.rules"
        label-width="120px"
        label-position="left"
        class="full-width"
      >
        <!-- 类型选择 -->
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="dialogObj.form.type" @change="onTypeChange">
            <el-radio label="directory">目录</el-radio>
            <el-radio label="menu">菜单</el-radio>
            <el-radio label="button">按钮</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 目录名称/菜单名称/按钮名称 -->
        <el-form-item :label="getNameLabel()" prop="menuName">
          <el-input
            v-model="dialogObj.form.menuName"
            maxlength="16"
            placeholder="请输入名称"
            clearable
            show-word-limit
          />
        </el-form-item>

        <!-- 上级菜单 -->
        <el-form-item
          v-if="dialogObj.form.type !== 'directory'"
          label="上级菜单"
          prop="parentId"
        >
          <el-select
            v-model="dialogObj.form.parentId"
            placeholder="请选择上级菜单"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in getParentOptions()"
              :key="item.menuId"
              :label="item.menuName"
              :value="item.menuId"
            />
          </el-select>
        </el-form-item>

        <!-- 前端权限地址 -->
        <el-form-item
          v-if="dialogObj.form.type !== 'directory'"
          label="前端权限地址"
        >
          <el-input
            v-model="dialogObj.form.frontendUrl"
            maxlength="64"
            placeholder="请输入前端权限地址"
            clearable
            show-word-limit
          />
        </el-form-item>

        <!-- 后端权限地址 -->
        <el-form-item
          v-if="dialogObj.form.type !== 'directory'"
          label="后端权限地址"
        >
          <el-input
            v-model="dialogObj.form.backendUrl"
            maxlength="64"
            placeholder="请输入后端权限地址"
            clearable
            show-word-limit
          />
        </el-form-item>

        <!-- 排序 -->
        <el-form-item
          v-if="dialogObj.form.type !== 'button'"
          label="排序"
        >
          <el-input-number
            v-model="dialogObj.form.sort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width: 150px"
          />
        </el-form-item>

        <!-- 目录图标 -->
        <el-form-item
          v-if="dialogObj.form.type === 'directory'"
          label="目录图标"
        >
          <el-select
            v-model="dialogObj.form.icon"
            placeholder="请选择图标"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in iconOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">
                <i :class="`el-icon-${item.value}`" style="margin-right: 8px" />
                {{ item.label }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 描述 -->
        <el-form-item label="描述">
          <el-input
            v-model="dialogObj.form.description"
            type="textarea"
            :rows="3"
            maxlength="200"
            placeholder="请输入描述信息"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogObj.visible = false">取消</el-button>
        <el-button
          v-preventReClick
          type="primary"
          :loading="isloading"
          @click="handleSubmit"
        >保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import menuApi from '@/api/menuApi'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  menuName: '',
  menuId: ''
}

const defaultMenuForm = {
  menuName: '',
  parentId: '',
  type: 'directory',
  icon: '',
  sort: 1,
  frontendUrl: '',
  backendUrl: '',
  description: ''
}

export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      isloading: false,
      addPermission: checkPermission(['menu_add']),
      deletePermission: checkPermission(['menu_delete']),
      updatePermission: checkPermission(['menu_modify']),
      searchForm: { ...defaultSearchForm },
      tableData: [],
      iconOptions: [],
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },
      dialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultMenuForm),
        rules: {
          type: [
            { required: true, message: '类型不能为空', trigger: 'change' }
          ],
          menuName: [
            { required: true, message: '名称不能为空', trigger: 'blur' },
            { max: 16, message: '长度不能超过16个字符', trigger: 'blur' }
          ],
          parentId: [
            { required: true, message: '上级菜单不能为空', trigger: 'change' }
          ]
        }
      }
    }
  },
  created() {
    this.onSearch()
  },
  methods: {
    checkPermission,


    onSearch() {
      this.pagination.pageNum = 1
      this.fetchData()
    },

    onReset() {
      this.searchForm = { ...defaultSearchForm }
      this.pagination.pageNum = 1
      this.fetchData()
    },

    // 获取数据
    async fetchData() {
      const params = {
        menuName: this.searchForm.menuName,
        menuId: this.searchForm.menuId,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }

      const res = await menuApi.getMenuList(params)
      if (res.data) {
        // 直接使用API返回的数据，添加hasChildren属性
        this.tableData = this.processMenuData(res.data.list || res.data)
        console.log('tableData', this.tableData)
        // 如果API返回了分页信息，更新分页数据
        if (res.data.total !== undefined) {
          this.pagination.total = this.tableData.length
        } else {
          // 如果没有分页信息，设置总数为当前数据长度
          this.pagination.total = this.tableData.length
        }
      }
    },

    // 处理菜单数据，添加必要的属性
    processMenuData(data) {
      if (!Array.isArray(data)) return []

      return data.map(item => {
        const processedItem = {
          ...item,
        }

        // 递归处理子菜单
        if (item.subMenu && Array.isArray(item.subMenu)) {
          processedItem.subMenu = this.processMenuData(item.subMenu)
        }

        return processedItem
      })
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.fetchData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.fetchData()
    },



    // 获取类型标签类型
    getTypeTagType(permType) {
      if (!permType) return 'info'

      const typeMap = {
        'MENU': 'success',
        'DIRECTORY': 'primary',
        'BUTTON': 'info'
      }

      // 如果permType是对象，取name属性
      const typeName = typeof permType === 'object' ? permType.name : permType
      return typeMap[typeName] || 'info'
    },

    // 获取类型文本
    getTypeText(permType) {
      if (!permType) return '-'

      const typeMap = {
        'MENU': '菜单',
        'DIRECTORY': '目录',
        'BUTTON': '按钮'
      }

      // 如果permType是对象，优先取text属性，否则取name属性
      if (typeof permType === 'object') {
        return permType.text || typeMap[permType.name] || permType.name || '-'
      }

      return typeMap[permType] || permType
    },

    // 获取名称标签
    getNameLabel() {
      const labelMap = {
        'directory': '目录名称',
        'menu': '菜单名称',
        'button': '按钮名称'
      }
      return labelMap[this.dialogObj.form.type] || '名称'
    },

    // 获取上级菜单选项
    getParentOptions() {
      const flatList = this.flattenTree(this.tableData)
      if (this.dialogObj.form.type === 'menu') {
        // 菜单只能选择目录作为上级
        return flatList.filter(item => {
          if (item.permType && typeof item.permType === 'object') {
            return item.permType.name === 'DIRECTORY'
          }
          return item.pid === 'root' // 顶级菜单通常是目录
        })
      } else if (this.dialogObj.form.type === 'button') {
        // 按钮只能选择菜单作为上级
        return flatList.filter(item => {
          if (item.permType && typeof item.permType === 'object') {
            return item.permType.name === 'MENU'
          }
          return item.pid !== 'root' // 非顶级菜单通常是菜单
        })
      }
      return []
    },

    // 扁平化树形数据
    flattenTree(tree) {
      const result = []
      const traverse = (nodes) => {
        nodes.forEach(node => {
          result.push(node)
          if (node.subMenu && node.subMenu.length > 0) {
            traverse(node.subMenu)
          }
        })
      }
      traverse(tree)
      return result
    },

    // 类型改变事件
    onTypeChange() {
      this.dialogObj.form.parentId = ''
      this.dialogObj.form.icon = ''
      this.dialogObj.form.sort = 1
      this.dialogObj.form.frontendUrl = ''
      this.dialogObj.form.backendUrl = ''
    },

    // 新增
    handleAdd(parentRow = null) {
      this.dialogObj.visible = true
      this.dialogObj.type = 'add'
      this.dialogObj.title = '新增'

      // 创建默认表单数据
      const formData = Object.assign({}, defaultMenuForm)

      // 如果有父级行数据，根据父级类型设置默认类型和上级菜单
      if (parentRow) {
        // 获取父级类型
        let parentType = 'directory'
        if (parentRow.permType) {
          if (typeof parentRow.permType === 'object') {
            switch (parentRow.permType.name) {
              case 'DIRECTORY':
                parentType = 'directory'
                break
              case 'MENU':
                parentType = 'menu'
                break
              case 'BUTTON':
                parentType = 'button'
                break
            }
          } else {
            switch (parentRow.permType) {
              case 'DIRECTORY':
                parentType = 'directory'
                break
              case 'MENU':
                parentType = 'menu'
                break
              case 'BUTTON':
                parentType = 'button'
                break
            }
          }
        }

        // 根据父级类型设置默认的新增类型
        if (parentType === 'directory') {
          // 目录下可以新增目录或菜单，默认新增菜单
          formData.type = 'menu'
          formData.parentId = parentRow.menuId
        } else if (parentType === 'menu') {
          // 菜单下可以新增按钮，默认新增按钮
          formData.type = 'button'
          formData.parentId = parentRow.menuId
        } else {
          // 按钮下不能新增子项，保持默认
          formData.type = 'directory'
        }
      }

      this.dialogObj.form = formData
      this.$nextTick(() => {
        this.$refs.menuFormRef.clearValidate()
      })
    },

    // 修改
    handleEdit(row) {
      this.dialogObj.visible = true
      this.dialogObj.type = 'update'
      this.dialogObj.title = '修改'

      // 转换API数据结构到表单结构
      let type = 'menu'
      if (row.permType) {
        if (typeof row.permType === 'object') {
          switch (row.permType.name) {
            case 'CATALOG':
              type = 'directory'
              break
            case 'BUTTON':
              type = 'button'
              break
            default:
              type = 'menu'
          }
        } else {
          // 如果permType是字符串，直接映射
          switch (row.permType) {
            case 'DIRECTORY':
              type = 'directory'
              break
            case 'BUTTON':
              type = 'button'
              break
            default:
              type = 'menu'
          }
        }
      }

      this.dialogObj.form = {
        menuId: row.menuId,
        menuName: row.menuName,
        parentId: row.pid === 'root' ? '' : row.pid,
        type: type,
        icon: row.permIcon || '',
        sort: parseInt(row.sort) || 1,
        frontendUrl: row.serverPermPath || '',
        backendUrl: '',
        description: row.permDesc || ''
      }
      this.$nextTick(() => {
        this.$refs.menuFormRef.clearValidate()
      })
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定对【${row.menuName}】进行删除操作吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          menuId: row.menuId
        }
        menuApi.deleteMenu(params).then((res) => {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.fetchData()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },

    // 提交
    handleSubmit() {
      this.$refs.menuFormRef.validate((valid) => {
        if (valid) {
          this.isloading = true
          const apiMethod = this.dialogObj.type === 'add' ? menuApi.addMenu : menuApi.modifyMenu

          apiMethod(this.dialogObj.form).then((res) => {
            this.$message({
              type: 'success',
              message: res.message
            })
            this.fetchData()
            this.dialogObj.visible = false
            this.isloading = false
          }).catch(() => {
            this.isloading = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.full-width {
  width: 100%;
}

.el-table {
  .el-table__row {
    .el-table__cell {
      .cell {
        .el-tag {
          margin-right: 5px;
        }
      }
    }
  }
}

// 图标选择器样式
.el-select-dropdown__item {
  .el-icon {
    margin-right: 8px;
  }
}

// 分页样式
.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
