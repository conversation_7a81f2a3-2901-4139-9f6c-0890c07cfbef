<template>
  <el-dialog
    :title="config.title"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="config.type === 'detail'"
    >
      <el-row :gutter="20">
        <!-- 活动编号 - 仅修改时显示 -->
        <el-col v-if="config.type === 'edit' || config.type === 'detail'" :span="12">
          <el-form-item label="活动编号:">
            <el-input v-model="formData.id" disabled />
          </el-form-item>
        </el-col>
        
        <!-- 活动名称 -->
        <el-col :span="12">
          <el-form-item label="活动名称:" prop="actTitle">
            <el-input 
              v-model="formData.actTitle" 
              placeholder="请输入活动名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动标题 -->
        <el-col :span="12">
          <el-form-item label="活动标题:" prop="actCaption">
            <el-input 
              v-model="formData.actCaption" 
              placeholder="请输入活动标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动类型 -->
        <el-col :span="12">
          <el-form-item label="活动类型:" prop="typeTitle">
            <el-select 
              v-model="formData.typeTitle" 
              placeholder="请选择活动类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in activityTypeOptions"
                :key="item.typeId"
                :label="item.typeTitle"
                :value="item.typeTitle"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <!-- 头图 -->
        <el-col :span="24">
          <el-form-item label="头图:" prop="headerImg">
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :file-list="fileList"
              :on-success="handleUploadSuccess"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              list-type="picture-card"
              :limit="5"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                支持jpg、png、JPEG、gif、svg格式，单张图片大小不超过500k
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        
        <!-- 排序 -->
        <el-col :span="12">
          <el-form-item label="排序:">
            <el-input-number 
              v-model="formData.sort" 
              :min="0"
              :max="9999"
              placeholder="默认为0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动门槛 -->
        <el-col :span="12">
          <el-form-item label="活动门槛:" prop="actThreshold">
            <el-checkbox-group v-model="thresholdList">
              <el-checkbox label="ALL">所有用户</el-checkbox>
              <el-checkbox label="NORMAL">普通客户</el-checkbox>
              <el-checkbox label="POTENTIAL">潜力客户</el-checkbox>
              <el-checkbox label="GOLD">金卡客户</el-checkbox>
              <el-checkbox label="PLATINUM">白金卡客户</el-checkbox>
              <el-checkbox label="DIAMOND">钻石客户</el-checkbox>
              <el-checkbox label="PRIVATE">私人银行客户</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        
        <!-- 报名时间 -->
        <el-col :span="24">
          <el-form-item label="报名时间:" prop="registerTime">
            <el-date-picker
              v-model="registerTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="报名开始时间"
              end-placeholder="报名结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动时间 -->
        <el-col :span="24">
          <el-form-item label="活动时间:" prop="activityTime">
            <el-date-picker
              v-model="activityTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="活动开始时间"
              end-placeholder="活动结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动地点 -->
        <el-col :span="12">
          <el-form-item label="活动地点:" prop="location">
            <el-input 
              v-model="formData.location" 
              placeholder="请输入活动地点"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <!-- 详细地址 -->
        <el-col :span="12">
          <el-form-item label="详细地址:">
            <el-input 
              v-model="formData.localDetail" 
              placeholder="请输入详细地址"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动名额 -->
        <el-col :span="12">
          <el-form-item label="活动名额:" prop="numRage">
            <el-input-number 
              v-model="formData.numRage" 
              :min="0"
              :max="99999"
              placeholder="0表示不限制"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动须知 -->
        <el-col :span="24">
          <el-form-item label="活动须知:" prop="actNotice">
            <el-input 
              v-model="formData.actNotice" 
              type="textarea"
              :rows="4"
              placeholder="请输入活动须知"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <!-- 活动介绍 -->
        <el-col :span="24">
          <el-form-item label="活动介绍:" prop="actDesc">
            <el-input
              v-model="formData.actDesc"
              type="textarea"
              :rows="6"
              placeholder="请输入活动介绍"
            />
          </el-form-item>
        </el-col>

        <!-- 报名填写项 -->
        <el-col :span="24">
          <el-form-item label="报名填写项:">
            <RegistrationForm v-model="registrationFormData" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ config.type === 'detail' ? '返回' : '取消' }}</el-button>
      <el-button 
        v-if="config.type !== 'detail'" 
        type="primary" 
        @click="handleSave"
        :loading="saving"
      >保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addActivity, modifyActivity, getActivityType } from '@/api/actManage'
import { getToken } from '@/utils/auth'
import RegistrationForm from './registrationForm.vue'

export default {
  name: 'ActivityDialog',
  components: {
    RegistrationForm
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      saving: false,
      activityTypeOptions: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      thresholdList: [],
      registerTime: [],
      activityTime: [],
      registrationFormData: {},
      formData: {
        id: null,
        actTitle: '',
        actCaption: '',
        typeTitle: '',
        headerImg: '',
        headerImgId: null,
        sort: 0,
        actThreshold: '',
        numRage: 0,
        location: '',
        localDetail: '',
        actNotice: '',
        actDesc: '',
        registerStartTime: '',
        registerEndTime: '',
        actStartTime: '',
        actEndTime: ''
      },
      rules: {
        actTitle: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        actCaption: [
          { required: true, message: '请输入活动标题', trigger: 'blur' }
        ],
        typeTitle: [
          { required: true, message: '请选择活动类型', trigger: 'change' }
        ],
        headerImg: [
          { required: true, message: '请上传头图', trigger: 'change' }
        ],
        actThreshold: [
          { required: true, message: '请选择活动门槛', trigger: 'change' }
        ],
        registerTime: [
          { required: true, message: '请选择报名时间', trigger: 'change' }
        ],
        activityTime: [
          { required: true, message: '请选择活动时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入活动地点', trigger: 'blur' }
        ],
        numRage: [
          { required: true, message: '请输入活动名额', trigger: 'blur' }
        ],
        actNotice: [
          { required: true, message: '请输入活动须知', trigger: 'blur' }
        ],
        actDesc: [
          { required: true, message: '请输入活动介绍', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible(val) {
      this.visible = val
      if (val) {
        this.initData()
      }
    },
    thresholdList: {
      handler(val) {
        this.formData.actThreshold = val.join(',')
      },
      deep: true
    },
    registerTime: {
      handler(val) {
        if (val && val.length === 2) {
          this.formData.registerStartTime = val[0]
          this.formData.registerEndTime = val[1]
        }
      },
      deep: true
    },
    activityTime: {
      handler(val) {
        if (val && val.length === 2) {
          this.formData.actStartTime = val[0]
          this.formData.actEndTime = val[1]
        }
      },
      deep: true
    }
  },
  created() {
    this.loadActivityTypes()
  },
  methods: {
    // 初始化数据
    initData() {
      if (this.config.formData) {
        this.formData = { ...this.config.formData }

        // 处理活动门槛
        if (this.formData.actThreshold) {
          this.thresholdList = this.formData.actThreshold.split(',')
        }

        // 处理时间
        if (this.formData.registerStartTime && this.formData.registerEndTime) {
          this.registerTime = [this.formData.registerStartTime, this.formData.registerEndTime]
        }
        if (this.formData.actStartTime && this.formData.actEndTime) {
          this.activityTime = [this.formData.actStartTime, this.formData.actEndTime]
        }

        // 处理图片
        if (this.formData.headerImg) {
          this.fileList = [{
            name: 'image',
            url: this.formData.headerImg,
            uid: Date.now()
          }]
        }

        // 处理报名填写项
        if (this.formData.registerInfo) {
          try {
            this.registrationFormData = JSON.parse(this.formData.registerInfo)
          } catch (error) {
            console.error('解析报名信息失败:', error)
            this.registrationFormData = {}
          }
        }
      } else {
        this.resetForm()
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        actTitle: '',
        actCaption: '',
        typeTitle: '',
        headerImg: '',
        headerImgId: null,
        sort: 0,
        actThreshold: '',
        numRage: 0,
        location: '',
        localDetail: '',
        actNotice: '',
        actDesc: '',
        registerStartTime: '',
        registerEndTime: '',
        actStartTime: '',
        actEndTime: ''
      }
      this.thresholdList = []
      this.registerTime = []
      this.activityTime = []
      this.fileList = []
      this.registrationFormData = {}
    },

    // 加载活动类型
    async loadActivityTypes() {
      try {
        const res = await getActivityType({
          delFlag: '0',
          actTypeStatus: 1
        })
        if (res.code === 200) {
          this.activityTypeOptions = res.data.list || []
        }
      } catch (error) {
        console.error('加载活动类型失败:', error)
      }
    },

    // 上传前验证
    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'].includes(file.type)
      const isLt500K = file.size / 1024 < 500

      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、JPEG、GIF、SVG 格式的图片!')
        return false
      }
      if (!isLt500K) {
        this.$message.error('图片大小不能超过 500KB!')
        return false
      }
      return true
    },

    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.formData.headerImg = response.data.url
        this.formData.headerImgId = response.data.id
        this.$message.success('上传成功')
      } else {
        this.$message.error(response.msg || '上传失败')
        // 移除失败的文件
        this.fileList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    // 移除图片
    handleRemove(file, fileList) {
      this.fileList = fileList
      if (fileList.length === 0) {
        this.formData.headerImg = ''
        this.formData.headerImgId = null
      }
    },

    // 验证时间
    validateTime() {
      if (this.registerTime.length === 2 && this.activityTime.length === 2) {
        const registerEnd = new Date(this.registerTime[1])
        const activityStart = new Date(this.activityTime[0])

        if (registerEnd > activityStart) {
          this.$message.error('活动开始时间不得早于报名结束时间，请重新设置')
          return false
        }
      }
      return true
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return

        if (!this.validateTime()) return

        this.saving = true
        try {
          const isEdit = this.config.type === 'edit'
          const apiMethod = isEdit ? modifyActivity : addActivity

          const submitData = { ...this.formData }

          // 处理报名填写项数据
          if (this.registrationFormData && Object.keys(this.registrationFormData).length > 0) {
            submitData.registerInfo = JSON.stringify(this.registrationFormData)
          }

          const res = await apiMethod(submitData)
          if (res.code === 200) {
            this.$message.success(isEdit ? '修改成功' : '新增成功')
            this.$emit('saveSuccess')
            this.handleClose()
          } else {
            this.$message.error(res.msg || (isEdit ? '修改失败' : '新增失败'))
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error('保存失败，请重试')
        } finally {
          this.saving = false
        }
      })
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

::v-deep .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.el-checkbox-group {
  .el-checkbox {
    margin-right: 15px;
    margin-bottom: 10px;
  }
}
</style>
